<!--
 * @Description: 签到功能页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02
 * @Layout: 最近30天日历 + 连签统计 + 签到按钮 + 签到记录
-->
<template>
  <view class="sign-page">
    <!-- 连签统计卡片 -->
    <view class="sign-status-card">
      <view class="status-info">
        <view class="status-title">连续签到</view>
        <view class="status-number">{{ continuousDays }}</view>
        <view class="status-unit">天</view>
      </view>
      <view class="today-status">
        <view class="today-label">今日状态</view>
        <view class="today-text" :class="{ 'signed': isSignedToday }">
          {{ isSignedToday ? '已签到' : '未签到' }}
        </view>
      </view>
    </view>

    <!-- 最近30天日历 -->
    <view class="calendar-section">
      <view class="section-title">最近30天签到记录</view>
      <view class="calendar-container">
        <view class="calendar-grid">
          <view
            class="calendar-day"
            v-for="(day, index) in calendarDays"
            :key="index"
            :class="{
              'signed': day.signed,
              'today': day.isToday,
              'future': day.isFuture
            }"
          >
            <view class="day-number">{{ day.day }}</view>
            <view class="day-status" v-if="day.signed">✓</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 签到按钮 -->
    <view class="sign-button-container">
      <view
        class="sign-button"
        :class="{ 'signed': isSignedToday, 'disabled': isSignedToday }"
        @click="handleSign"
      >
        <text class="sign-text">{{ isSignedToday ? '今日已签到' : '立即签到' }}</text>
      </view>
    </view>

    <!-- 签到记录 -->
    <view class="record-section">
      <view class="section-title">签到记录</view>
      <view class="record-list">
        <view
          class="record-item"
          v-for="(record, index) in signRecords"
          :key="index"
        >
          <view class="record-date">{{ record.date }}</view>
          <view class="record-status">{{ record.status }}</view>
          <view class="record-time">{{ record.time }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SignIndex',
  data() {
    return {
      // 签到状态
      isSignedToday: false,
      continuousDays: 5,

      // 最近30天日历数据
      calendarDays: [],

      // 签到记录
      signRecords: [
        { date: '2025-08-01', status: '已签到', time: '09:15' },
        { date: '2025-07-31', status: '已签到', time: '08:30' },
        { date: '2025-07-30', status: '已签到', time: '10:22' },
        { date: '2025-07-29', status: '已签到', time: '07:45' },
        { date: '2025-07-28', status: '已签到', time: '09:08' },
        { date: '2025-07-27', status: '已签到', time: '08:55' },
        { date: '2025-07-26', status: '已签到', time: '09:33' }
      ]
    }
  },

  onLoad() {
    this.checkSignStatus()
    this.generateCalendarDays()
  },

  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 生成最近30天的日历数据
     */
    generateCalendarDays() {
      const days = []
      const today = new Date()

      // 生成最近30天的数据
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(today.getDate() - i)

        const dayData = {
          day: date.getDate(),
          date: date.toISOString().split('T')[0],
          signed: this.isDateSigned(date),
          isToday: i === 0,
          isFuture: false
        }

        days.push(dayData)
      }

      this.calendarDays = days
    },

    /**
     * 检查指定日期是否已签到
     */
    isDateSigned(date) {
      const dateStr = date.toISOString().split('T')[0]
      return this.signRecords.some(record => record.date === dateStr)
    },

    /**
     * 检查签到状态
     */
    checkSignStatus() {
      // 这里应该调用API检查今日签到状态
      // 暂时使用模拟数据
      const today = new Date().toDateString()
      const lastSignDate = uni.getStorageSync('lastSignDate')
      this.isSignedToday = lastSignDate === today
    },

    /**
     * 处理签到
     */
    handleSign() {
      if (this.isSignedToday) {
        uni.showToast({
          title: '今日已签到',
          icon: 'none'
        })
        return
      }

      // 执行签到
      this.performSign()
    },

    /**
     * 执行签到操作
     */
    performSign() {
      // 这里应该调用API执行签到
      // 暂时使用本地存储模拟
      const today = new Date().toDateString()
      uni.setStorageSync('lastSignDate', today)

      this.isSignedToday = true
      this.continuousDays += 1

      // 添加签到记录
      const now = new Date()
      const timeStr = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`

      this.signRecords.unshift({
        date: now.toISOString().split('T')[0],
        status: '已签到',
        time: timeStr
      })

      // 更新日历显示
      this.generateCalendarDays()

      uni.showToast({
        title: '签到成功！',
        icon: 'success'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sign-page {
  min-height: calc(100vh - 100rpx);
  background: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 连签统计卡片 */
.sign-status-card {
  background: #fff;
  margin: 20rpx 30rpx;
  padding: 40rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.status-info {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.status-title {
  font-size: 28rpx;
  color: #666;
}

.status-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007aff;
}

.status-unit {
  font-size: 24rpx;
  color: #666;
}

.today-status {
  text-align: center;
}

.today-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.today-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.today-text.signed {
  color: #51cf66;
}

/* 日历区域 */
.calendar-section {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.calendar-container {
  padding: 20rpx;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background: #f8f9fa;
  position: relative;
  min-height: 80rpx;
}

.calendar-day.signed {
  background: #e8f5e8;
  border: 2rpx solid #51cf66;
}

.calendar-day.today {
  background: #e3f2fd;
  border: 2rpx solid #007aff;
}

.calendar-day.today.signed {
  background: #e8f5e8;
  border: 2rpx solid #51cf66;
}

.calendar-day.future {
  background: #f0f0f0;
  opacity: 0.5;
}

.day-number {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.day-status {
  font-size: 20rpx;
  color: #51cf66;
  margin-top: 4rpx;
}

/* 签到按钮 */
.sign-button-container {
  padding: 20rpx 30rpx;
}

.sign-button {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
  max-width: 300rpx;
  margin: 0 auto;
}

.sign-button.signed {
  background: #e9ecef;
  box-shadow: none;
}

.sign-button.disabled {
  opacity: 0.6;
}

.sign-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}

.sign-button.signed .sign-text {
  color: #666;
}

/* 通用区域样式 */
.reward-section,
.record-section {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 奖励列表 */
.reward-list {
  padding: 20rpx;
}

.reward-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
}

.reward-item.today {
  background: #e3f2fd;
  border-color: #2196f3;
}

.reward-item.received {
  background: #e8f5e8;
  border-color: #4caf50;
}

.reward-day {
  font-size: 24rpx;
  color: #666;
  width: 100rpx;
}

.reward-icon {
  font-size: 32rpx;
  margin: 0 20rpx;
}

.reward-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.reward-status {
  font-size: 24rpx;
  color: #666;
}

.reward-item.today .reward-status {
  color: #2196f3;
  font-weight: bold;
}

.reward-item.received .reward-status {
  color: #4caf50;
}

/* 签到记录 */
.record-list {
  padding: 20rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-date {
  font-size: 28rpx;
  color: #333;
  width: 200rpx;
}

.record-status {
  flex: 1;
  font-size: 26rpx;
  color: #51cf66;
  text-align: center;
}

.record-reward {
  font-size: 26rpx;
  color: #666;
  width: 150rpx;
  text-align: right;
}
</style>
