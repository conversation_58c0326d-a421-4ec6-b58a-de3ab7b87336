<!--
 * @Description: 签到功能页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02
 * @Layout: 最近30天日历 + 连签统计 + 签到按钮 + 签到记录
-->
<template>
  <view class="sign-page">
    <!-- 连签统计卡片 -->
    <view class="sign-status-card">
      <view class="status-info">
        <view class="status-title">连续签到</view>
        <view class="status-number">{{ continuousDays }}</view>
        <view class="status-unit">天</view>
      </view>
      <view class="today-status" @click="handleSign">
        <view class="sign-button" :class="{ 'signed': isSignedToday }">
          <text class="sign-text">{{ isSignedToday ? '已签到' : '签到' }}</text>
          <text class="sign-icon" v-if="isSignedToday">✓</text>
        </view>
      </view>
    </view>

    <!-- 最近30天日历 -->
    <view class="calendar-section">
      <view class="section-title">最近30天签到记录</view>
      <view class="calendar-container">
        <!-- 周标题 -->
        <view class="week-header">
          <view class="week-day" v-for="day in weekDays" :key="day">{{ day }}</view>
        </view>
        <!-- 日历网格 -->
        <view class="calendar-grid">
          <view
            class="calendar-day"
            v-for="(day, index) in calendarDays"
            :key="index"
            :class="{
              'signed': day.signed,
              'today': day.isToday,
              'future': day.isFuture
            }"
          >
            <view class="day-number">{{ day.day }}</view>
            <view class="day-status" v-if="day.signed">✓</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 签到记录 -->
    <view class="record-section">
      <view class="section-title">签到记录</view>
      <view class="record-list">
        <view
          class="record-item"
          v-for="(record, index) in signRecords"
          :key="index"
        >
          <view class="record-date">{{ record.date }}</view>
          <view class="record-status">{{ record.status }}</view>
          <view class="record-time">{{ record.time }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SignIndex',
  data() {
    return {
      // 签到状态
      isSignedToday: false,
      continuousDays: 5,

      // 周标题
      weekDays: ['一', '二', '三', '四', '五', '六', '日'],

      // 最近30天日历数据
      calendarDays: [],

      // 签到记录
      signRecords: [
        { date: '2025-08-01', status: '已签到', time: '09:15' },
        { date: '2025-07-31', status: '已签到', time: '08:30' },
        { date: '2025-07-30', status: '已签到', time: '10:22' },
        { date: '2025-07-29', status: '已签到', time: '07:45' },
        { date: '2025-07-28', status: '已签到', time: '09:08' },
        { date: '2025-07-27', status: '已签到', time: '08:55' },
        { date: '2025-07-26', status: '已签到', time: '09:33' }
      ]
    }
  },

  onLoad() {
    this.checkSignStatus()
    this.generateCalendarDays()
  },

  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 生成最近30天的日历数据
     */
    generateCalendarDays() {
      const days = []
      const today = new Date()

      // 生成最近30天的数据
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(today.getDate() - i)

        const dayData = {
          day: date.getDate(),
          date: date.toISOString().split('T')[0],
          signed: this.isDateSigned(date),
          isToday: i === 0,
          isFuture: false
        }

        days.push(dayData)
      }

      this.calendarDays = days
    },

    /**
     * 检查指定日期是否已签到
     */
    isDateSigned(date) {
      const dateStr = date.toISOString().split('T')[0]
      return this.signRecords.some(record => record.date === dateStr)
    },

    /**
     * 检查签到状态
     */
    checkSignStatus() {
      // 这里应该调用API检查今日签到状态
      // 暂时使用模拟数据
      const today = new Date().toDateString()
      const lastSignDate = uni.getStorageSync('lastSignDate')
      this.isSignedToday = lastSignDate === today
    },

    /**
     * 处理签到
     */
    handleSign() {
      if (this.isSignedToday) {
        uni.showToast({
          title: '今日已签到',
          icon: 'none'
        })
        return
      }

      // 执行签到
      this.performSign()
    },

    /**
     * 执行签到操作
     */
    performSign() {
      // 这里应该调用API执行签到
      // 暂时使用本地存储模拟
      const today = new Date().toDateString()
      uni.setStorageSync('lastSignDate', today)

      this.isSignedToday = true
      this.continuousDays += 1

      // 添加签到记录
      const now = new Date()
      const timeStr = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`

      this.signRecords.unshift({
        date: now.toISOString().split('T')[0],
        status: '已签到',
        time: timeStr
      })

      // 更新日历显示
      this.generateCalendarDays()

      uni.showToast({
        title: '签到成功！',
        icon: 'success'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sign-page {
  min-height: calc(100vh - 100rpx);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 40rpx;
}

/* 连签统计卡片 */
.sign-status-card {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  margin: 20rpx 30rpx;
  padding: 40rpx;
  border-radius: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(255, 154, 158, 0.3);
  position: relative;
  overflow: hidden;
}

.sign-status-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.status-info {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  z-index: 1;
}

.status-title {
  font-size: 28rpx;
  color: #fff;
  opacity: 0.9;
}

.status-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}

.status-unit {
  font-size: 24rpx;
  color: #fff;
  opacity: 0.9;
}

.today-status {
  z-index: 1;
}

.sign-button {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(79, 172, 254, 0.4);
  display: flex;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.sign-button.signed {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  box-shadow: 0 4rpx 16rpx rgba(168, 237, 234, 0.4);
}

.sign-text {
  color: #fff;
  font-size: 26rpx;
  font-weight: 600;
}

.sign-icon {
  color: #fff;
  font-size: 20rpx;
}

/* 日历区域 */
.calendar-section {
  background: rgba(255, 255, 255, 0.95);
  margin: 20rpx 30rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.calendar-container {
  padding: 20rpx;
}

.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.week-day {
  text-align: center;
  font-size: 24rpx;
  color: #666;
  font-weight: 600;
  padding: 12rpx 0;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  background: #f8f9fa;
  position: relative;
  min-height: 80rpx;
  transition: all 0.3s ease;
}

.calendar-day.signed {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  box-shadow: 0 4rpx 12rpx rgba(168, 237, 234, 0.3);
  transform: scale(1.05);
}

.calendar-day.today {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4rpx 12rpx rgba(79, 172, 254, 0.4);
  transform: scale(1.1);
}

.calendar-day.today.signed {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 154, 158, 0.4);
}

.calendar-day.future {
  background: #f0f0f0;
  opacity: 0.5;
}

.day-number {
  font-size: 24rpx;
  color: #333;
  font-weight: 600;
}

.calendar-day.today .day-number,
.calendar-day.signed .day-number {
  color: #fff;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.1);
}

.day-status {
  font-size: 20rpx;
  color: #fff;
  margin-top: 4rpx;
  font-weight: bold;
}



/* 通用区域样式 */
.record-section {
  background: rgba(255, 255, 255, 0.95);
  margin: 20rpx 30rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid rgba(240, 240, 240, 0.5);
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  margin: 0;
}

/* 签到记录 */
.record-list {
  padding: 20rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid rgba(240, 240, 240, 0.3);
  background: linear-gradient(90deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  margin-bottom: 8rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.record-item:hover {
  background: linear-gradient(90deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
  transform: translateX(8rpx);
}

.record-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.record-date {
  font-size: 28rpx;
  color: #333;
  width: 200rpx;
  font-weight: 600;
}

.record-status {
  flex: 1;
  font-size: 26rpx;
  color: #51cf66;
  text-align: center;
  font-weight: 600;
}

.record-time {
  font-size: 26rpx;
  color: #666;
  width: 120rpx;
  text-align: right;
  font-weight: 500;
}
</style>
