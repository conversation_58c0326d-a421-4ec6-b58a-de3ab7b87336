<!--
 * @Description: 签到功能页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02
 * @Layout: 签到日历 + 签到奖励 + 连续签到记录
-->
<template>
  <view class="sign-page">
    <!-- 签到状态卡片 -->
    <view class="sign-status-card">
      <view class="status-info">
        <view class="status-title">今日签到状态</view>
        <view class="status-text" :class="{ 'signed': isSignedToday }">
          {{ isSignedToday ? '已签到' : '未签到' }}
        </view>
      </view>
      <view class="continuous-days">
        <view class="days-number">{{ continuousDays }}</view>
        <view class="days-label">连续签到天数</view>
      </view>
    </view>

    <!-- 签到按钮 -->
    <view class="sign-button-container">
      <view
        class="sign-button"
        :class="{ 'signed': isSignedToday, 'disabled': isSignedToday }"
        @click="handleSign"
      >
        <text class="sign-text">{{ isSignedToday ? '今日已签到' : '立即签到' }}</text>
        <text class="sign-icon">{{ isSignedToday ? '✓' : '+' }}</text>
      </view>
    </view>

    <!-- 签到奖励 -->
    <view class="reward-section">
      <view class="section-title">签到奖励</view>
      <view class="reward-list">
        <view
          class="reward-item"
          v-for="(reward, index) in rewardList"
          :key="index"
          :class="{ 'received': reward.received, 'today': reward.isToday }"
        >
          <view class="reward-day">第{{ reward.day }}天</view>
          <view class="reward-icon">{{ reward.icon }}</view>
          <view class="reward-name">{{ reward.name }}</view>
          <view class="reward-status">
            {{ reward.received ? '已领取' : (reward.isToday ? '今日奖励' : '未达成') }}
          </view>
        </view>
      </view>
    </view>

    <!-- 签到记录 -->
    <view class="record-section">
      <view class="section-title">最近签到记录</view>
      <view class="record-list">
        <view
          class="record-item"
          v-for="(record, index) in signRecords"
          :key="index"
        >
          <view class="record-date">{{ record.date }}</view>
          <view class="record-status">{{ record.status }}</view>
          <view class="record-reward">{{ record.reward }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SignIndex',
  data() {
    return {
      // 签到状态
      isSignedToday: false,
      continuousDays: 5,

      // 奖励列表
      rewardList: [
        { day: 1, icon: '🎁', name: '积分+10', received: true, isToday: false },
        { day: 2, icon: '💎', name: '钻石+5', received: true, isToday: false },
        { day: 3, icon: '🎫', name: '优惠券', received: true, isToday: false },
        { day: 4, icon: '⚡', name: '模型次数+2', received: true, isToday: false },
        { day: 5, icon: '🏆', name: '经验+50', received: true, isToday: false },
        { day: 6, icon: '🎊', name: '神秘礼包', received: false, isToday: true },
        { day: 7, icon: '👑', name: 'VIP体验卡', received: false, isToday: false }
      ],

      // 签到记录
      signRecords: [
        { date: '2025-08-01', status: '已签到', reward: '积分+10' },
        { date: '2025-07-31', status: '已签到', reward: '钻石+5' },
        { date: '2025-07-30', status: '已签到', reward: '优惠券' },
        { date: '2025-07-29', status: '已签到', reward: '模型次数+2' },
        { date: '2025-07-28', status: '已签到', reward: '经验+50' }
      ]
    }
  },

  onLoad() {
    this.checkSignStatus()
  },

  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 检查签到状态
     */
    checkSignStatus() {
      // 这里应该调用API检查今日签到状态
      // 暂时使用模拟数据
      const today = new Date().toDateString()
      const lastSignDate = uni.getStorageSync('lastSignDate')
      this.isSignedToday = lastSignDate === today
    },

    /**
     * 处理签到
     */
    handleSign() {
      if (this.isSignedToday) {
        uni.showToast({
          title: '今日已签到',
          icon: 'none'
        })
        return
      }

      // 执行签到
      this.performSign()
    },

    /**
     * 执行签到操作
     */
    performSign() {
      // 这里应该调用API执行签到
      // 暂时使用本地存储模拟
      const today = new Date().toDateString()
      uni.setStorageSync('lastSignDate', today)

      this.isSignedToday = true
      this.continuousDays += 1

      // 更新今日奖励状态
      const todayReward = this.rewardList.find(item => item.isToday)
      if (todayReward) {
        todayReward.received = true
      }

      // 添加签到记录
      this.signRecords.unshift({
        date: new Date().toISOString().split('T')[0],
        status: '已签到',
        reward: todayReward ? todayReward.name : '积分+10'
      })

      uni.showToast({
        title: '签到成功！',
        icon: 'success'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sign-page {
  min-height: 100vh;
  background: #f5f5f5;
}


/* 签到状态卡片 */
.sign-status-card {
  background: #fff;
  margin: 20rpx 30rpx;
  padding: 40rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.status-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.status-text.signed {
  color: #51cf66;
}

.continuous-days {
  text-align: center;
}

.days-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 8rpx;
}

.days-label {
  font-size: 24rpx;
  color: #666;
}

/* 签到按钮 */
.sign-button-container {
  padding: 0 30rpx 40rpx;
}

.sign-button {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  border-radius: 50rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.3);
}

.sign-button.signed {
  background: #e9ecef;
  box-shadow: none;
}

.sign-button.disabled {
  opacity: 0.6;
}

.sign-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 12rpx;
}

.sign-button.signed .sign-text {
  color: #666;
}

.sign-icon {
  color: #fff;
  font-size: 28rpx;
}

.sign-button.signed .sign-icon {
  color: #51cf66;
}

/* 通用区域样式 */
.reward-section,
.record-section {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 奖励列表 */
.reward-list {
  padding: 20rpx;
}

.reward-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
}

.reward-item.today {
  background: #e3f2fd;
  border-color: #2196f3;
}

.reward-item.received {
  background: #e8f5e8;
  border-color: #4caf50;
}

.reward-day {
  font-size: 24rpx;
  color: #666;
  width: 100rpx;
}

.reward-icon {
  font-size: 32rpx;
  margin: 0 20rpx;
}

.reward-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.reward-status {
  font-size: 24rpx;
  color: #666;
}

.reward-item.today .reward-status {
  color: #2196f3;
  font-weight: bold;
}

.reward-item.received .reward-status {
  color: #4caf50;
}

/* 签到记录 */
.record-list {
  padding: 20rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-date {
  font-size: 28rpx;
  color: #333;
  width: 200rpx;
}

.record-status {
  flex: 1;
  font-size: 26rpx;
  color: #51cf66;
  text-align: center;
}

.record-reward {
  font-size: 26rpx;
  color: #666;
  width: 150rpx;
  text-align: right;
}
</style>
